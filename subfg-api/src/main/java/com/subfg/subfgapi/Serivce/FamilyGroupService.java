package com.subfg.subfgapi.Serivce;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.subfg.common.constans.FamilyGroupConstants;
import com.subfg.common.exception.BusinessException;
import com.subfg.common.util.IdGeneratorUtil;
import com.subfg.common.util.TimeUtil;
import com.subfg.domain.entity.fg.FamilyGroupPo;
import com.subfg.domain.entity.fg.FgMemberPo;
import com.subfg.domain.request.createGroupBuyingFamilyGroupReq;
import com.subfg.domain.request.createSelfBuiltFamilyGroupReq;
import com.subfg.domain.request.getFamilyGroupListReq;
import com.subfg.domain.vo.FamilyGroupDetailVo;
import com.subfg.domain.vo.FamilyGroupVo;
import com.subfg.repository.mapper.FamilyGroupMapper;
import com.subfg.repository.mapper.FgMemberMapper;

import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 家庭组服务类
 * 统一处理自建家庭组和拼团家庭组
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FamilyGroupService {

    private final FamilyGroupMapper familyGroupMapper;
    private final FgMemberMapper fgMemberMapper;

    /**
     * 创建自建家庭组
     */
    @Transactional(rollbackFor = Exception.class)
    public String createSelfBuiltFamilyGroup(createSelfBuiltFamilyGroupReq req) {
        log.info("开始创建自建家庭组，请求参数：{}", req);
        
        String currentUserId = StpUtil.getLoginIdAsString();
        
        // 创建家庭组记录
        String familyGroupId = createSelfBuiltFamilyGroupRecord(req, currentUserId);
        
        // 创建团长成员记录
        createGroupLeaderMember(familyGroupId, currentUserId);
        
        log.info("自建家庭组创建成功，家庭组ID：{}", familyGroupId);
        return familyGroupId;
    }

    /**
     * 创建拼团家庭组
     */
    @Transactional(rollbackFor = Exception.class)
    public String createGroupBuyingFamilyGroup(createGroupBuyingFamilyGroupReq req) {
        log.info("开始创建拼团家庭组，请求参数：{}", req);
        
        String currentUserId = StpUtil.getLoginIdAsString();
        
        // 验证截止时间
        validateDeadline(req.getDeadline());
        
        // 创建家庭组记录
        String familyGroupId = createGroupBuyingFamilyGroupRecord(req, currentUserId);
        
        // 创建团长成员记录
        createGroupLeaderMember(familyGroupId, currentUserId, req);
        
        log.info("拼团家庭组创建成功，家庭组ID：{}", familyGroupId);
        return familyGroupId;
    }

    /**
     * 验证截止时间
     */
    private void validateDeadline(Long deadline) {
        Long currentTime = TimeUtil.getCurrentTimestamp();
        
        // 检查截止时间不能是过去时间
        if (deadline <= currentTime) {
            throw new BusinessException("family.group.deadline.invalid");
        }
        
        // 检查截止时间不能超过30天
        Long maxDeadline = currentTime + (30 * 24 * 60 * 60 * 1000L);
        if (deadline > maxDeadline) {
            throw new BusinessException("family.group.deadline.too.far");
        }
    }

    /**
     * 创建自建家庭组记录
     */
    private String createSelfBuiltFamilyGroupRecord(createSelfBuiltFamilyGroupReq req, String currentUserId) {
        String familyGroupId = IdGeneratorUtil.generateFamilyGroupId();
        Long currentTime = TimeUtil.getCurrentTimestamp();
        
        FamilyGroupPo familyGroup = new FamilyGroupPo()
            .setFamilyGroupId(familyGroupId)
            .setFamilyGroupName(req.getFamilyGroupName().trim())
            .setDescription(req.getDescription() != null ? req.getDescription().trim() : null)
            .setGroupType(FamilyGroupConstants.GroupType.SELF_BUILT) // 设置为自建类型
            .setFamilyGroupStatus(FamilyGroupConstants.Status.REVIEWING) // 设置为审核中
            .setProductId(req.getProductId())
            .setRegionId(req.getRegionId())
            .setPlanId(req.getPlanId())
            .setAmount(req.getAmount())
            .setBillingCycle(req.getBillingCycle() != null ? req.getBillingCycle() : FamilyGroupConstants.Defaults.DEFAULT_BILLING_CYCLE)
            .setCurrentMemberCount(FamilyGroupConstants.Defaults.DEFAULT_INITIAL_MEMBER_COUNT) // 初始成员数为1（创建者）
            .setSumVacancy(req.getFamilyGroupTotalVacancy())
            .setGroupLeaderId(currentUserId)
            .setCreateUserId(currentUserId)
            .setCreateTime(currentTime)
            .setUpdateTime(currentTime)
            .setLatestJoinTime(currentTime);

        int result = familyGroupMapper.insert(familyGroup);
        if (result <= 0) {
            throw new BusinessException(FamilyGroupConstants.ErrorCode.CREATE_FAILED);
        }
        
        return familyGroupId;
    }

    /**
     * 创建拼团家庭组记录
     */
    private String createGroupBuyingFamilyGroupRecord(createGroupBuyingFamilyGroupReq req, String currentUserId) {
        String familyGroupId = IdGeneratorUtil.generateFamilyGroupId();
        Long currentTime = TimeUtil.getCurrentTimestamp();
        
        FamilyGroupPo familyGroup = new FamilyGroupPo()
            .setFamilyGroupId(familyGroupId)
            .setFamilyGroupName(req.getFamilyGroupName().trim())
            .setDescription(req.getDescription() != null ? req.getDescription().trim() : null)
            .setGroupType(FamilyGroupConstants.GroupType.GROUP_BUYING) // 设置为拼团类型
            .setFamilyGroupStatus(FamilyGroupConstants.Status.BUILDING) // 设置为组建中状态
            .setProductId(req.getProductId())
            .setRegionId(req.getRegionId())
            .setPlanId(req.getPlanId())
            .setAmount(req.getAmount()) 
            .setBillingCycle(req.getBillingCycle() != null ? req.getBillingCycle() : FamilyGroupConstants.Defaults.DEFAULT_BILLING_CYCLE)
            .setCurrentMemberCount(FamilyGroupConstants.Defaults.DEFAULT_INITIAL_MEMBER_COUNT) // 初始成员数为1（创建者）
            .setSumVacancy(req.getFamilyGroupTotalVacancy())
            .setDeadline(req.getDeadline()) // 设置截止时间
            .setGroupLeaderId(null) // 拼团家庭组创建时没有团长
            .setCreateUserId(currentUserId)
            .setCreateTime(currentTime)
            .setUpdateTime(currentTime);

        int result = familyGroupMapper.insert(familyGroup);
        if (result <= 0) {
            throw new BusinessException(FamilyGroupConstants.ErrorCode.CREATE_FAILED);
        }
        
        return familyGroupId;
    }

    /**
     * 创建团长成员记录（自建家庭组）
     */
    private void createGroupLeaderMember(String familyGroupId, String currentUserId) {
        String memberId = IdGeneratorUtil.generateFamilyMemberId();
        Long currentTime = TimeUtil.getCurrentTimestamp();
        
        FgMemberPo member = new FgMemberPo()
            .setMemberId(memberId)
            .setFamilyGroupId(familyGroupId)
            .setUserId(currentUserId)
            .setStatus(FamilyGroupConstants.MemberStatus.ACTIVATED) // 团长直接激活
            .setCreateTime(currentTime)
            .setUpdateTime(currentTime);

        int result = fgMemberMapper.insert(member);
        if (result <= 0) {
            throw new BusinessException(FamilyGroupConstants.ErrorCode.CREATE_FAILED);
        }
    }

    /**
     * 创建团长成员记录（拼团家庭组）
     */
    private void createGroupLeaderMember(String familyGroupId, String currentUserId, createGroupBuyingFamilyGroupReq req) {
        String memberId = IdGeneratorUtil.generateFamilyMemberId();
        Long currentTime = TimeUtil.getCurrentTimestamp();
        
        FgMemberPo member = new FgMemberPo()
            .setMemberId(memberId)
            .setFamilyGroupId(familyGroupId)
            .setUserId(currentUserId)
            .setStatus(FamilyGroupConstants.MemberStatus.PENDING) // 拼团所有成员都是待激活 有团长后统一全部激活
            .setCreateTime(currentTime)
            .setUpdateTime(currentTime);

        int result = fgMemberMapper.insert(member);
        if (result <= 0) {
            throw new BusinessException(FamilyGroupConstants.ErrorCode.CREATE_FAILED);
        }
    }

    /**
     * 获取家庭组分页列表
     */
    public List<FamilyGroupVo> getFamilyGroupList(getFamilyGroupListReq req) {
        log.info("开始查询家庭组列表，请求参数：{}", req);

        // 构建查询条件
        LambdaQueryWrapper<FamilyGroupPo> queryWrapper = new LambdaQueryWrapper<>();

        // 家庭组类型
        if (req.getGroupType() != null) {
            queryWrapper.eq(FamilyGroupPo::getGroupType, req.getGroupType());
        }

        // 家庭组状态
        if (req.getStatus() != null) {
            queryWrapper.eq(FamilyGroupPo::getFamilyGroupStatus, req.getStatus());
        }

        // 产品ID
        if (req.getProductId() != null) {
            queryWrapper.eq(FamilyGroupPo::getProductId, req.getProductId());
        }

        // 地区ID
        if (req.getRegionId() != null) {
            queryWrapper.eq(FamilyGroupPo::getRegionId, req.getRegionId());
        }

        // 团长用户ID
        if (StringUtils.hasText(req.getGroupLeaderId())) {
            queryWrapper.eq(FamilyGroupPo::getGroupLeaderId, req.getGroupLeaderId());
        }

        // 创建用户ID
        if (StringUtils.hasText(req.getCreateUserId())) {
            queryWrapper.eq(FamilyGroupPo::getCreateUserId, req.getCreateUserId());
        }

        // 家庭组名称关键字模糊搜索
        if (StringUtils.hasText(req.getKeyword())) {
            queryWrapper.like(FamilyGroupPo::getFamilyGroupName, req.getKeyword());
        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(FamilyGroupPo::getCreateTime);

        // 分页查询
        Page<FamilyGroupPo> page = new Page<>(req.getPageNum(), req.getPageSize());
        Page<FamilyGroupPo> result = familyGroupMapper.selectPage(page, queryWrapper);

        // 转换为VO对象
        List<FamilyGroupVo> voList = result.getRecords().stream()
            .map(this::convertToVo)
            .collect(Collectors.toList());

        log.info("家庭组列表查询完成，共查询到{}条记录", voList.size());
        return voList;
    }

    /**
     * 将实体对象转换为VO对象
     */
    private FamilyGroupVo convertToVo(FamilyGroupPo po) {
        FamilyGroupVo vo = new FamilyGroupVo();
        vo.setFamilyGroupId(po.getFamilyGroupId());
        vo.setFamilyGroupName(po.getFamilyGroupName());
        vo.setDescription(po.getDescription());
        vo.setGroupType(po.getGroupType());
        vo.setGroupTypeName(getGroupTypeName(po.getGroupType()));
        vo.setFamilyGroupStatus(po.getFamilyGroupStatus());
        vo.setStatusName(getStatusName(po.getFamilyGroupStatus()));
        vo.setProductId(po.getProductId());
        vo.setRegionId(po.getRegionId());
        vo.setPlanId(po.getPlanId());
        vo.setAmount(po.getAmount());
        vo.setBillingCycle(po.getBillingCycle());
        vo.setCurrentMemberCount(po.getCurrentMemberCount());
        vo.setSumVacancy(po.getSumVacancy());
        vo.setDeadline(po.getDeadline());
        vo.setGroupLeaderId(po.getGroupLeaderId());
        vo.setCreateUserId(po.getCreateUserId());
        vo.setCreateTime(po.getCreateTime());
        vo.setUpdateTime(po.getUpdateTime());
        vo.setLatestJoinTime(po.getLatestJoinTime());
        vo.setLaunchTime(po.getLaunchTime());
        vo.setIsConverted(po.getIsConverted());
        return vo;
    }

    /**
     * 获取家庭组类型名称
     */
    private String getGroupTypeName(Integer groupType) {
        if (groupType == null) {
            return null;
        }
        switch (groupType) {
            case FamilyGroupConstants.GroupType.SELF_BUILT:
                return "自建家庭组";
            case FamilyGroupConstants.GroupType.GROUP_BUYING:
                return "拼团家庭组";
            default:
                return "未知类型";
        }
    }

    /**
     * 获取家庭组状态名称
     */
    private String getStatusName(Integer status) {
        if (status == null) {
            return null;
        }
        switch (status) {
            case FamilyGroupConstants.Status.REVIEWING:
                return "审核中";
            case FamilyGroupConstants.Status.BUILDING:
                return "组建中";
            case FamilyGroupConstants.Status.LAUNCHED:
                return "已发车";
            case FamilyGroupConstants.Status.CLOSED:
                return "已关闭";
            case FamilyGroupConstants.Status.REVIEW_REJECTED:
                return "审核未通过";
            default:
                return "未知状态";
        }
    }

    /**
     * 获取随机推荐家庭组列表
     */
    public List<FamilyGroupVo> getRecommendFamilyGroupList(){
        // 获取组建中且未满员的家庭组
        List<FamilyGroupPo> list = familyGroupMapper.selectRandomAvailableGroups(
            FamilyGroupConstants.Status.BUILDING, 5);

        return list.stream().map(this::convertToVo).collect(Collectors.toList());
    }

    /**
     * 获取家庭组详情信息
     */
    public FamilyGroupDetailVo getFamilyGroupDetail(String familyGroupId) {
        log.info("开始查询家庭组详情，家庭组ID：{}", familyGroupId);

        // 参数校验
        if (!StringUtils.hasText(familyGroupId)) {
            throw new BusinessException("family.group.id.required");
        }

        // 查询家庭组详情
        FamilyGroupDetailVo detailVo = familyGroupMapper.selectFamilyGroupDetailById(familyGroupId);

        if (detailVo == null || detailVo.getFamilyGroup() == null) {
            throw new BusinessException("family.group.not.found");
        }

        return detailVo;
    }

}
